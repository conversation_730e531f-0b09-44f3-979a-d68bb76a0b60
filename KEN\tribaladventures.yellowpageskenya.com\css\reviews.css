/* Client Reviews Section */
.client-reviews {
  background-color: #ffffff;
  padding: 4rem 0;
  position: relative;
}

.client-reviews .section-title {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  color: #212529;
  margin-bottom: 3rem;
}

.reviews-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.review-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.review-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(216, 71, 43, 0.15);
  border-color: #dfa649;
}

.review-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 4rem;
  color: #a62716;
  font-family: Georgia, serif;
  line-height: 1;
}

.review-content {
  margin-bottom: 1.5rem;
}

.review-content p {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
  font-style: italic;
  padding-top: 1rem;
}

.review-author {
  border-top: 2px solid #dfa649;
  padding-top: 1rem;
}

.review-author h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #a62716;
}

/* Star rating decoration */
.review-card::after {
  content: '★★★★★';
  position: absolute;
  top: 1rem;
  right: 1.5rem;
  color: #dfa649;
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .client-reviews {
    padding: 3rem 0;
  }
  
  .client-reviews .section-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
  
  .reviews-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }
  
  .review-card {
    padding: 1.5rem;
  }
  
  .review-content p {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .client-reviews {
    padding: 2rem 0;
  }
  
  .client-reviews .section-title {
    font-size: 1.8rem;
  }
  
  .review-card {
    padding: 1.2rem;
  }
  
  .review-card::before {
    font-size: 3rem;
    top: -5px;
  }
}
