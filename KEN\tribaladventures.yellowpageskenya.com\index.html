<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>
      Tribal Adventures | Luxury Safaris
    </title>
    <meta
      name="description"
      content="Tribal Adventures offers luxury safaris and tailor-made holidays in Kenya and beyond. Enjoy personalized travel, exclusive lodges, and unforgettable adventures."
    />

    <meta name="keywords" content="Tribal Adventures" />

    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <!-- Favicon -->
    <link rel="icon" href="./favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="./favicon/apple-touch-icon.jpg"
    />
    <link
      rel="icon"
      type="image/jpg"
      sizes="32x32"
      href="./favicon/favicon-32x32.jpg"
    />
    <link
      rel="icon"
      type="image/jpg"
      sizes="16x16"
      href="./favicon/favicon-16x16.jpg"
    />
    <link rel="manifest" href="./favicon/site.webmanifest.json" />
    <!-- Robots -->
    <meta name="robots" content="index, follow" />
    <!-- Site Published Date -->
    <meta property="article:published_time" content="2025-09-01" />
    <!-- Google Verification -->
    <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->

    <!-- <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" /> -->

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:url"
      content="https://tribaladventures.yellowpageskenya.com"
    />
    <meta
      property="og:title"
      content="Tribal Adventures | Luxury Safaris & Tailor-Made Travel"
    />
    <meta
      property="og:description"
      content="Tribal Adventures offers luxury safaris and tailor-made holidays in Kenya and beyond. Enjoy personalized travel, exclusive lodges, and unforgettable adventures."
    />
    <meta
      property="og:image"
      content="https://tribaladventures.yellowpageskenya.com/img/logo.webp"
    />
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:site" content="@yellowpages254" />
    <meta
      property="twitter:url"
      content="https://tribaladventures.yellowpageskenya.com "
    />
    <meta
      property="twitter:title"
      content="Tribal Adventures | Luxury Safaris & Tailor-Made Travel "
    />
    <meta
      property="twitter:description"
      content="Tribal Adventures offers luxury safaris and tailor-made holidays in Kenya and beyond. Enjoy personalized travel, exclusive lodges, and unforgettable adventures."
    />
    <meta
      property="twitter:image"
      content="https://tribaladventures.yellowpageskenya.com/img/logo.webp"
    />
    <!-- Canonical URL -->
    <link
      rel="canonical"
      href="https://tribaladventures.yellowpageskenya.com"
    />
    <!-- Hreflang tags -->
    <link
      rel="alternate"
      hreflang="en"
      href="https://tribaladventures.yellowpageskenya.com"
    />
    <!-- Include more hreflang tags here if you have the website available in other languages -->
    <!-- Sitemap -->
    <link
      rel="sitemap"
      type="application/xml"
      title="Sitemap"
      href="https://tribaladventures.yellowpageskenya.com/sitemap.xml"
    />

    <!-- Preconnect to Google Maps APIs -->
    <link rel="preconnect" href="https://maps.googleapis.com" crossorigin />
    <link rel="preconnect" href="https://maps.gstatic.com" crossorigin />

    <link
      rel="preload"
      as="image"
      href="./img/Hero_image_tddfs5_c_scale,w_1351.webp"
      fetchpriority="high"
    />
    <link
      rel="preload"
      as="image"
      href="./img/Hero_image_tddfs5_c_scale,w_200.webp"
      fetchpriority="high"
    />

    <!-- Internal CSS -->

    <link rel="stylesheet" href="css/features.css" />
    <link rel="stylesheet" href="css/ots.css" />
    <link rel="stylesheet" href="css/s2.css" />
    <link rel="stylesheet" href="css/hero.css" />
    <link rel="stylesheet" href="css/service-section.css" />
    <link rel="stylesheet" href="css/mn.css" />
    <link rel="stylesheet" href="css/about.css" />
    <link rel="stylesheet" href="css/main.css" />
    <link rel="stylesheet" href="css/services.css" />
    <link rel="stylesheet" href="css/testimonial.css" />
    <link rel="stylesheet" href="css/categories.css" />
    <link rel="stylesheet" href="css/reviews.css" />

    <!-- <link rel="alternate" hreflang="en" href="https://tribaladventures.yellowpageskenya.com/"> -->
    <link
      rel="alternate"
      hreflang="x-default"
      href="https://tribaladventures.yellowpageskenya.com/"
    />
   <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-DBYXC0788L"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-DBYXC0788L');
</script>

    <style>
      html {
        scroll-behavior: smooth;
      }

      /* Inline critical font styles for Poppins */
      @font-face {
        font-family: "Poppins";
        font-style: normal;
        font-weight: 400;
        src: local("Poppins Regular"), local("Poppins-Regular"),
          url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2)
            format("woff2");
        font-display: swap;
      }

      @font-face {
        font-family: "Poppins";
        font-style: normal;
        font-weight: 700;
        src: local("Poppins Bold"), local("Poppins-Bold"),
          url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2)
            format("woff2");
        font-display: swap;
      }

      /* Inline critical font styles for Work Sans */

      @font-face {
        font-family: "Work Sans";
        font-style: normal;
        font-weight: 400;
        src: local("Work Sans Regular"), local("WorkSans-Regular"),
          url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2)
            format("woff2");
        font-display: swap;
      }

      @font-face {
        font-family: "Work Sans";
        font-style: normal;
        font-weight: 600;
        src: local("Work Sans SemiBold"), local("WorkSans-SemiBold"),
          url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2)
            format("woff2");
        font-display: swap;
      }

      @font-face {
        font-family: "Work Sans";
        font-style: normal;
        font-weight: 700;
        src: local("Work Sans Bold"), local("WorkSans-Bold"),
          url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2)
            format("woff2");
        font-display: swap;
      }

      body {
        font-family: "Work Sans", sans-serif;
      }

      .mobile-menu {
        transition: transform 0.3s ease-in-out;
      }

      .mobile-menu.hidden {
        transform: translateX(-100%);
      }

      #top-bar {
        transition: transform 0.3s ease-out, opacity 0.3s ease-out;
      }

      #main-nav {
        transition: all 0.3s ease-out;
      }

      .sticky {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 50;
        background-color: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>

  <body>
    <div class="content-grid" data-theme="blue">
      <div id="top-bar">
        <div class="top-bar-inner">
          <div class="contact-info-topbar">
            <a
              href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <EMAIL>
            </a>
            <span class="separator"></span>
            <span class="working-hours">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 1 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              Nairobi, Kenya
            </span>
          </div>
          <div class="phone-and-social">
            <div class="phone-number phon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="icon"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              <a href="tel:0733874225">0733874225 </a>
              <a href="tel:0712419214"> , 0712419214</a>
            </div>

            <div class="social-icons">
              <!-- Facebook-->
              <a
                href="https://www.facebook.com/tribaladventures254"
                target="_blank"
                class="social-icon"
                aria-label="Facebook"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="m16.117 8.906l-2.589 3.086l-3.715-2.281a.5.5 0 0 0-.644.104l-3.052 3.636a.5.5 0 0 0 .766.643l2.774-3.306l3.715 2.281c.211.13.485.085.645-.104l2.866-3.416a.5.5 0 0 0-.766-.643M12 1C5.715 1 .975 5.594.975 11.686a10.43 10.43 0 0 0 3.471 7.905c.071.06.114.149.118.242l.058 1.867a1.343 1.343 0 0 0 1.883 1.187l2.088-.92a.33.33 0 0 1 .226-.018c1.037.283 2.107.425 3.181.422c6.285 0 11.025-4.594 11.025-10.685S18.285 1 12 1m0 20.371a11 11 0 0 1-2.916-.387a1.36 1.36 0 0 0-.894.068l-2.086.919a.35.35 0 0 1-.324-.026a.34.34 0 0 1-.158-.276l-.058-1.871a1.34 1.34 0 0 0-.45-.952a9.45 9.45 0 0 1-3.14-7.16C1.975 6.164 6.285 2 12 2s10.025 4.164 10.025 9.686S17.715 21.37 12 21.37"
                  />
                </svg>
              </a>

              <!-- Instagram-->
              <a
                href="https://www.instagram.com/tribaladventures/"
                target="_blank"
                class="social-icon"
                aria-label="Twitter"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  width="24"
                  height="24"
                  role="img"
                  aria-label="Instagram"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="1.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect
                    x="2.5"
                    y="2.5"
                    width="19"
                    height="19"
                    rx="5"
                    ry="5"
                  ></rect>
                  <circle cx="12" cy="12" r="3.25"></circle>
                  <circle
                    cx="17.5"
                    cy="6.5"
                    r="0.75"
                    fill="currentColor"
                    stroke="none"
                  ></circle>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid dark-theme">
      <div class="nav-inner">
        <div class="logo">
          <!-- <img src="./img/logo.webp" alt="Logo" title="Logo" width="190" height="53"> -->

          <img
            src="./img/logo.webp"
            srcset="./img/logo.webp 1x, ./img/logo.webp 2x"
            alt="Logo"
            title="Logo"
            width="auto"
            height="70"
          />
        </div>
        <div class="desktop-menu">
          <a href="/">Home</a>
          <a href="#about">About Us</a>
          <a href="#services">Services</a>
          <a href="#contact">Contact</a>
        </div>
        <button id="mobile-menu-toggle" class="mobile-menu-toggle">
          &#9776;
        </button>
        <a
          href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
          class="contact-btn"
          >Get In Touch</a
        >
      </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
      <div class="mobile-menu-content">
        <div class="menu-header">
          <!-- <img src="./img/logo.webp" alt="Logo" title="Logo"> -->

          <img
            src="./img/logo.webp"
            srcset="./img/logo.webp 1x, ./img/logo.webp 2x"
            alt="Logo"
            title="Logo"
            width="auto"
            height="70"
          />

          <button id="close-mobile-menu">&times;</button>
        </div>
        <div class="menu-links">
          <a href="/">Home</a>
          <a href="#about">About Us</a>
          <a href="#services">Services</a>
          <a href="#contact">Contact</a>

          <!-- button mobile menu -->
          <a
            href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
            class="contact-btn"
            >Get In Touch</a
          >
        </div>
      </div>
    </div>

    <section class="hero">
      <picture>
        <source
          media="(max-width: 799px)"
          type="image/webp"
          srcset="./img/Hero_image_tddfs5_c_scale,w_200.webp"
        />
        <source
          media="(min-width: 800px) and (max-width: 1214px)"
          type="image/webp"
          srcset="./img/Hero_image_tddfs5_c_scale,w_1215.webp"
        />
        <source
          media="(min-width: 1215px)"
          type="image/webp"
          srcset="./img/Hero_image_tddfs5_c_scale,w_1351.webp"
        />
        <img
          src="./img/Hero_image_tddfs5_c_scale,w_1351.webp"
          alt="Tribal Adventures – Your Trusted Travel Partner"
          class="hero-img"
          width="1351"
          height="594"
          decoding="async"
          fetchpriority="high"
        />
      </picture>

      <div class="text-overlay"></div>
      <div class="content-grid">
        <div class="hero-content">
          <div>
            <span class="h6">Tribal Adventures</span>
            <h1>Luxury Safaris & Tailor-Made Travel in Kenya and Beyond</h1>
            <p>
              Experience luxury safaris and tailor-made adventures with Tribal
              Adventures. From Kenya’s wild plains to idyllic beaches, we create
              journeys as unique as you are.
            </p>
            <a href="#services" class="explore-btn mt" style="color: #a62716"
              >View Services</a
            >
          </div>
        </div>
      </div>
    </section>

    <section class="features">
      <div class="pattern-overlay"></div>
      <div class="feature-container">
        <div class="content-grid">
          <div class="grid-container">
            <div class="text-content">
              <h2 class="animate-text">Core Values</h2>
              <p class="animate-text-delay">
                At the heart of Tribal Adventures are values that guide every
                journey we create.
              </p>
            </div>
            <div class="cards-container">
              <div class="card" data-tilt>
                <div class="card-header">
                  <div class="card-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"
                      />
                    </svg>
                  </div>
                  <h3>Personalization</h3>
                </div>

                <div class="card-overlay"></div>
              </div>

              <div class="card" data-tilt>
                <div class="card-header">
                  <div class="card-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke-width="1.5"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                      />
                    </svg>
                  </div>
                  <h3>Excellence</h3>
                </div>

                <div class="card-overlay"></div>
              </div>

              <div class="card" data-tilt>
                <div class="card-header">
                  <div class="card-icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3"
                      />
                    </svg>
                  </div>
                  <h3>Innovation</h3>
                </div>

                <div class="card-overlay"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="about-us" id="about">
      <div class="content-grid">
        <div class="about-content">
          <div class="about-image">
            <div class="image-container main-image">
              <picture>
                <source
                  media="(max-width: 499px)"
                  type="image/webp"
                  srcset="./img/about_image_bpomkf_c_scale,w_200.webp"
                />
                <source
                  media="(min-width: 500px)"
                  type="image/webp"
                  srcset="./img/about_image_bpomkf_c_scale,w_635.webp"
                />
                <img
                  src="./img/about_image_bpomkf_c_scale,w_635.webp"
                  alt="Tribal Adventures about image"
                  loading="lazy"
                />
              </picture>
            </div>

            <div class="image-container bottom-right-image">
              <picture>
                <source
                  srcset="
                    ./img/about_image-sm_pinoap_c_scale,w_200.webp 200w,
                    ./img/about_image-sm_pinoap_c_scale,w_265.webp 265w
                  "
                  sizes="(max-width: 265px) 100vw, 265px"
                />
                <img
                  src="./img/about_image-sm_pinoap_c_scale,w_265.webp"
                  alt="About image"
                  loading="lazy"
                />
              </picture>
            </div>
          </div>

          <div class="about-text">
            <span class="abt">About Us</span>

            <p>
              Welcome to Tribal Adventures, the beacon of luxury travel nestled
              in the vibrant heart of Nairobi, Kenya. Founded in 2015 by the
              visionary Emily Clifton, our agency has been sculpting unique
              travel experiences, setting us apart as a premier destination for
              those seeking adventure in East Africa and beyond. Under Emily's
              leadership, we've not only embraced the essence of luxury travel
              but redefined it, ensuring that each journey with us is nothing
              short of extraordinary.
            </p>

            <p>
              Tribal Adventures is a tour operator creating tailor-made holidays
              and honeymoons to amazing destinations here in Kenya and beyond.
              We are passionate about Kenya safari adventures, whether you’re
              looking for a family holiday, a romantic escape, an adventure
              somewhere extraordinary, or a special event, we can arrange it.
            </p>

            <p>
              We offer a wide range of personalized travel services designed to
              meet the diverse needs of travelers seeking luxury, adventure, and
              seamless experiences in East Africa and beyond. Explore our
              curated services and find out how we can make your travel dreams
              come true. Reach us today and book your spot!
            </p>

            <a href="#services" class="cta-button">View More</a>
          </div>
        </div>
      </div>
    </div>

    <section class="top-categories">
      <div class="container">
        <div class="section-header">
          <h2 class="cat-section-title">Featured Safaris</h2>
        </div>

        <div class="categories-grid">
          <div class="category-card">
            <div class="category-image">
              <img
                src="./img/maasai-maara.webp"
                alt="Classic Maasai Mara Package"
                title="Classic Maasai Mara Package"
                height="278"
                width="240"
              />
            </div>
            <div class="category-text-overlay">
              <h3 class="category-title">Classic Maasai Mara Package</h3>
            </div>
          </div>

          <div class="category-card">
            <div class="category-image">
              <img
                src="./img/honeymoon-safari.webp"
                alt="Romantic Honeymoon Package"
                title="Romantic Honeymoon Package"
                width="240"
                height="278"
              />
            </div>
            <div class="category-text-overlay">
              <h3 class="category-title">Romantic Honeymoon Package</h3>
            </div>
          </div>

          <div class="category-card">
            <div class="category-image">
              <img
                src="./img/family-safari.webp"
                alt="Safari Packages"
                title="Safari Packages"
                width="240"
                height="278"
              />
            </div>
            <div class="category-text-overlay">
              <h3 class="category-title">Family Adventure Safari</h3>
            </div>
          </div>

          <div class="category-card">
            <div class="category-image">
              <img
                src="./img/fly-in.webp"
                alt="Luxury Fly-In Safaris"
                title="Luxury Fly-In Safaris"
                width="240"
                height="278"
              />
            </div>
            <div class="category-text-overlay">
              <h3 class="category-title">Luxury Fly-In Safaris</h3>
            </div>
          </div>

          <div class="category-card">
            <div class="category-image">
              <img
                src="./img/great-migration.webp"
                alt="Great Migration Safari"
                title="Great Migration Safari"
                width="240"
                height="278"
              />
            </div>
            <div class="category-text-overlay">
              <h3 class="category-title">Great Migration Safari</h3>
            </div>
          </div>
        </div>
      </div>
    </section>

    <div class="services-container" id="services">
      <div class="services-header">
        <h2 class="products-section-title">Our Services</h2>
      </div>
      <div class="services-grid">
        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/holidays.webp"
              alt="Tailor-Made Holidays & Honeymoons"
              title="Tailor-Made Holidays & Honeymoons"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Tailor-Made Holidays & Honeymoons</h3>

            <p>
              Custom-designed itineraries for unforgettable escapes, crafted
              around your style, pace, and passions.
            </p>
          </div>
        </div>

        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/corporate-travel.webp"
              alt="Corporate & Leisure Travel"
              title="Corporate & Leisure Travel"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Corporate & Leisure Travel</h3>

            <p>
              Seamless travel solutions for business trips, team retreats, and
              personal getaways.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/hotel-reservations.webp"
              alt="Hotel & Villa Reservations"
              title="Hotel & Villa Reservations"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Hotel & Villa Reservations</h3>

            <p>
              Handpicked stays ranging from luxury hotels to private villas,
              tailored to your comfort.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/airport-transfer.webp"
              alt="Vehicle Hire & Airport Transfers"
              title="Vehicle Hire & Airport Transfers"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Vehicle Hire & Airport Transfers</h3>

            <p>
              Reliable cars, vans, and transfers to ensure smooth and
              stress-free travel.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/ticketing.webp"
              alt="Ticketing & Chartered Flights"
              title="Ticketing & Chartered Flights"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Ticketing & Chartered Flights</h3>

            <p>
              Hassle-free ticketing and private flight options for convenience
              and exclusivity.
            </p>
          </div>
        </div>

        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/mice.webp"
              alt="MICE (Meetings, Incentives, Conferences & Events)"
              title="MICE (Meetings, Incentives, Conferences & Events)"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>MICE (Meetings, Incentives, Conferences & Events)</h3>

            <p>
              Professional planning and execution of events that inspire and
              impress.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/Day Trips & Excursions.webp"
              alt="Day Trips & Excursions"
              title="Day Trips & Excursions"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Day Trips & Excursions</h3>

            <p>
              Curated short adventures that let you explore Kenya’s culture,
              wildlife, and landscapes.
            </p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img
              src="./img/cruises.webp"
              alt="Cruises"
              title="Cruises"
              loading="lazy"
              width="417"
              height="313"
            />
          </div>
          <div class="service-content">
            <h3>Cruises</h3>

            <p>
              Luxurious cruise experiences, blending relaxation and discovery
              across stunning destinations.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Client Reviews Section -->
    <section class="client-reviews">
      <div class="content-grid">
        <h2 class="section-title">What Our Clients Say</h2>
        <div class="reviews-container">
          <div class="review-card">
            <div class="review-content">
              <p>"Tribal Adventures are pocket friendly and they will give you the experience that you are looking for. Thank you so much for the amazing hook up. We had such a blast! We will be back soon."</p>
            </div>
            <div class="review-author">
              <h3>Nzilani Anne</h3>
            </div>
          </div>

          <div class="review-card">
            <div class="review-content">
              <p>"Emily and Tribal Adventures organized our safari to absolute perfection. Attentive to our needs, understood everything we were looking for. We were very fortunate to have Henry as our personal driver."</p>
            </div>
            <div class="review-author">
              <h3>Yara Karmiloff de Newbery</h3>
            </div>
          </div>

          <div class="review-card">
            <div class="review-content">
              <p>"Emily helped us arrange a wonderful trip to Diani. She was awesome in incorporating all of our wants and needs!!!! I will be using Tribal adventures again and again."</p>
            </div>
            <div class="review-author">
              <h3>Mueni Faith</h3>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="contact" id="contact">
      <div class="content-grid">
        <h2 class="section-title">Contact Us</h2>
        <div class="contact-content">
          <div class="contact-map">
            <iframe
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.791628219331!2d36.6894836!3d-1.2998418000000003!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f1d06dc196d03%3A0x909e41cf0c823d4a!2sThe%20Krafty%20Market!5e0!3m2!1sen!2ske!4v1756719356028!5m2!1sen!2ske"
              width="600"
              height="450"
              style="border: 0"
              allowfullscreen=""
              loading="lazy"
              referrerpolicy="no-referrer-when-downgrade"
              title="tribal adventures map"
            ></iframe>
          </div>
          <div class="contact-info">
            <div class="contact-item">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z"
                  ></path>
                </svg>
              </span>
              <div>
                <h3>Physical Location</h3>
                <p>Remote (Nairobi, Kenya)</p>
              </div>
            </div>
            <div class="contact-item">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6"
                  ></path>
                </svg>
              </span>
              <div>
                <h3>Phone number</h3>
                <p>
                  <a href="tel:0712419214"
                    >0733874225, 0712419214
                  </a>
                </p>
              </div>
            </div>
            <div class="contact-item">
              <span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4"
                  ></path></svg
              ></span>
              <div>
                <h3>Email</h3>
                <p>
                  <a
                    href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
                    ><EMAIL></a
                  >
                </p>
              </div>
            </div>
            <div class="contact-item">
              <span
                ><svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1.5rem"
                  height="1.5rem"
                  viewBox="0 0 512 512"
                  class="contact-icon"
                >
                  <path
                    fill="currentColor"
                    d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24"
                  ></path></svg
              ></span>
              <div>
                <h3>Working Hours</h3>
                <p>Monday to Friday: 8:00am – 5:00pm</p>

                <p>Weekends: 10:00am – 5:00pm</p>
              </div>
            </div>
            <!-- Social Media Icons -->
          </div>
        </div>
      </div>
    </section>

    <footer class="footer">
      <div class="content-grid">
        <div class="footer-content">
          <div class="copyright">
            <p>
              &copy; <span id="current-year"></span> Tribal Adventures. All Rights
              Reserved.
            </p>
          </div>
          <div class="designer">
            <a
              href="https://www.yellowpageskenya.com/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src="./img/yp_logo.webp"
                loading="lazy"
                alt="Yellow Pages Kenya"
                width="50"
                height="50"
                title="Yellow Pages Kenya"
              />
              <p>Powered by Yellow Pages Kenya.</p>
            </a>
          </div>
        </div>
      </div>
    </footer>

    <script src="./js/testimonial.js"></script>
    <!-- <script src="./js/main.js"></script> -->

    <!-- <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script> -->

    <script>
      document.getElementById("current-year").textContent =
        new Date().getFullYear();
    </script>

    <!-- <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script> -->

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const topBar = document.getElementById("top-bar");
        const mainNav = document.getElementById("main-nav");
        const mainContent = document.querySelector("body"); // Adjust this selector if needed
        const mobileMenu = document.getElementById("mobile-menu");
        const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
        const closeMobileMenu = document.getElementById("close-mobile-menu");
        const mobileMenuLinks = document.querySelectorAll(
          ".mobile-menu .menu-links a"
        );

        // Mobile Menu Logic
        mobileMenuToggle.addEventListener("click", () => {
          mobileMenu.classList.add("show");
        });

        closeMobileMenu.addEventListener("click", () => {
          mobileMenu.classList.remove("show");
        });

        // Add click event listeners to all mobile menu links
        mobileMenuLinks.forEach((link) => {
          link.addEventListener("click", function (event) {
            mobileMenu.classList.remove("show");

            const href = this.getAttribute("href");
            if (href.startsWith("#") && href !== "#") {
              event.preventDefault();

              const targetElement = document.querySelector(href);

              if (targetElement) {
                setTimeout(() => {
                  const yOffset = -80;
                  const y =
                    targetElement.getBoundingClientRect().top +
                    window.pageYOffset +
                    yOffset;

                  window.scrollTo({
                    top: y,
                    behavior: "smooth",
                  });
                }, 300);
              }
            }
          });
        });

        // Debug function to log sticky state
        function logStickyState() {
          //console.log('Scroll position:', window.scrollY);
          //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
          //console.log('mainNav style:', mainNav.style.cssText);
          //console.log('computed position:', window.getComputedStyle(mainNav).position);
        }

        // Improved Sticky Header Logic
        function handleScroll() {
          const scrollTop =
            window.scrollY || document.documentElement.scrollTop;
          //console.log('Scrolling, position:', scrollTop);

          if (scrollTop > 50) {
            // Make sure we're applying direct styles
            mainNav.style.position = "fixed";
            mainNav.style.top = "0";
            mainNav.style.left = "0";
            mainNav.style.width = "100%";
            mainNav.style.zIndex = "100";
            mainNav.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.1)";
            mainNav.classList.add("sticky");

            // Add padding to body to prevent content jump
            mainContent.style.paddingTop = mainNav.offsetHeight + "px";

            // Hide the top bar
            if (topBar) {
              topBar.style.display = "none";
            }

            logStickyState();
          } else {
            // Remove direct styles
            mainNav.style.position = "";
            mainNav.style.top = "";
            mainNav.style.left = "";
            mainNav.style.width = "";
            mainNav.style.zIndex = "";
            mainNav.style.boxShadow = "";
            mainNav.classList.remove("sticky");

            // Remove padding from body
            mainContent.style.paddingTop = "0";

            // Show the top bar on desktop
            if (topBar && window.innerWidth >= 1024) {
              topBar.style.display = "block";
            }

            logStickyState();
          }
        }

        // Initial check on page load
        handleScroll();

        // Add scroll event listener
        window.addEventListener("scroll", handleScroll);

        // Handle window resize
        window.addEventListener("resize", () => {
          if (window.innerWidth < 1024 && topBar) {
            topBar.style.display = "none";
          } else if (window.scrollY <= 50 && topBar) {
            topBar.style.display = "block";
          }

          // Recalculate sticky state on resize
          handleScroll();
        });
      });
    </script>

    <script>
      function throttle(fn, limit) {
        let waiting = false;
        return function (...args) {
          if (!waiting) {
            fn.apply(this, args);
            waiting = true;
            setTimeout(() => (waiting = false), limit);
          }
        };
      }

      function handleParallaxScroll() {
        const elements = document.querySelectorAll("[data-parallax]");
        const scrollY = window.scrollY;

        elements.forEach((el) => {
          const container = el.closest(".parallax-container");
          const rect = container.getBoundingClientRect();
          const offsetTop = container.offsetTop;
          const height = container.offsetHeight;

          // Only calculate if it's in view
          if (
            scrollY + window.innerHeight > offsetTop &&
            scrollY < offsetTop + height
          ) {
            const speed = 0.5; // Adjust this to control intensity
            const yPos = (scrollY - offsetTop) * speed;
            el.style.transform = `translateY(${yPos}px)`;
          }
        });
      }

      document.addEventListener("DOMContentLoaded", function () {
        window.addEventListener("scroll", throttle(handleParallaxScroll, 16)); // 60fps-ish
      });
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const lazySections = document.querySelectorAll(".lazy-background");

        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const el = entry.target;
                const bgUrl = el.dataset.bg;

                // Create a new image to preload the background
                const img = new Image();
                img.src = bgUrl;

                img.onload = function () {
                  // Only set background when fully loaded
                  el.style.backgroundImage = `url('${bgUrl}')`;
                  el.classList.add("loaded");
                };

                // Stop observing this element
                observer.unobserve(el);
              }
            });
          },
          {
            rootMargin: "200px", // Preload a bit before the element enters the viewport
            threshold: 0.1,
          }
        );

        lazySections.forEach((section) => observer.observe(section));
      });
    </script>
  </body>
</html>
